# Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration
# MONGODB_URI=mongodb://localhost:27017/your-database
# For MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/your-database?retryWrites=true&w=majority&appName=Cluster0

# Upstash Redis Configuration (for rate limiting)
# UPSTASH_REDIS_REST_URL=your-upstash-redis-url
# UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# Other Configuration
# JWT_SECRET=your-jwt-secret-key
# API_KEY=your-api-key
