{"name": "@upstash/core-analytics", "version": "v0.0.10", "main": "./dist/index.js", "types": "./dist/index.d.ts", "license": "MIT", "private": false, "keywords": ["analytics", "upstash", "serverless"], "bugs": {"url": "https://github.com/upstash/core-analytics/issues"}, "homepage": "https://github.com/upstash/core-analytics#readme", "files": ["./dist/**"], "author": "<PERSON> <<EMAIL>>", "scripts": {"build": "tsup", "test": "jest --collect-coverage", "fmt": "bunx rome check . --apply-suggested && bunx rome format . --write"}, "devDependencies": {"rome": "^11.0.0", "typescript": "^5.0.0", "bun-types": "latest", "tsup": "latest"}, "dependencies": {"@upstash/redis": "^1.28.3"}, "engines": {"node": ">=16.0.0"}}